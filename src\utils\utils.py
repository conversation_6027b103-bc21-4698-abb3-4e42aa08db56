# -*- coding: utf-8 -*-
import logging
import re

logger = logging.getLogger(__name__)

# Dictionary for translations
translations = {
    'ar': {
        'welcome_trading_ai': "أهلاً بك في دورة تعلم التداول بالذكاء الاصطناعي! 📈",
        'add_gemini_key_prompt': "للبدء، يرجى إضافة مفتاح Gemini API الخاص بك. هذا سيسمح لي بتوليد محتوى تعليمي مخصص لك.",
        'add_gemini_key_button': "🔑 إضافة مفتاح Gemini API",
        'gemini_access_error': "عذرًا، لا يمكنني الوصول إلى Gemini الآن. يرجى التأكد من إضافة مفتاح API صالح.",
        'gemini_tutor_error': "حدث خطأ أثناء محاولة الإجابة على سؤالك. يرجى المحاولة مرة أخرى لاحقًا.",
        'lesson_generation_error': "عذرًا، لا يمكنني الوصول إلى Gemini الآن لتوليد الدرس. يرجى التأكد من إضافة مفتاح API صالح.",
        'chapter_button': "الفصل {number}",
        'next_chapter_button': "الفصل التالي ❯",
        'take_quiz_button': "📝 إجراء الاختبار",
        'ask_tutor_prompt': "يمكنك أيضًا طرح أي سؤال يتعلق بالتداول وسأبذل قصارى جهدي للإجابة عليه باستخدام Gemini.",
        'ask_tutor_button': '❓ اسأل مدرس الذكاء الاصطناعي',
        'ask_tutor_instruction': "يرجى كتابة سؤالك المتعلق بالتداول.",
        'quiz_already_started': "📝 يبدو أنك بدأت الاختبار بالفعل. يرجى إكمال الإجابة على الأسئلة.",
        'course_completed': "لقد أكملت الدورة الأساسية والاختبار! 🎉",
        'error_starting_course': "حدث خطأ ما. يرجى محاولة بدء الدورة مرة أخرى باستخدام /learn_trading_ai",
        'tutor_prompt_header': "أجب على سؤال التداول التالي كمدرس خبير باللغة {lang}:\n\nالسؤال: {question}\n\nالإجابة:",
        'chapter_gen_prompt_header': "أنت مدرس تداول خبير ومؤلف محتوى تعليمي باللغة {lang}. مهمتك هي إنشاء محتوى تعليمي للفصل رقم {chapter_number} من دورة تداول للمبتدئين مكونة من 10 فصول.",
        'guide_add_gemini_key': "يرجى استخدام الأمر /add_gemini_key لإضافة مفتاح API الخاص بك.",
        'complete_chapters_first': "يرجى إكمال جميع الفصول قبل إجراء الاختبار.",
        'quiz_starting': "بدء الاختبار...",
        'quiz_results': "انتهى الاختبار! نتيجتك: {score}/{total}",
        'ask_again_prompt': "يمكنك طرح سؤال آخر أو الانتقال إلى الفصل التالي.",
        'operation_in_progress': "⏳ جاري إنشاء الفصل... يرجى الانتظار.",
        'quiz_generation_in_progress': "⏳ جاري إنشاء الاختبار... يرجى الانتظار.",
        'continue_or_ask': "يمكنك الآن المتابعة في الفصل أو طرح سؤال آخر.",
        'review_material_title': "مواد للمراجعة",
        'supplementary_chapters': "📚 فصول تكميلية مخصصة",
        'back_to_main': "🏠 العودة إلى القائمة الرئيسية",
        'next_steps': "ماذا تريد أن تفعل الآن؟",
        'generating_chapter': "⏳ جاري إنشاء الفصل التكميلي...",
        'chapter_not_found': "الفصل المطلوب غير موجود.",
        'chapter_generation_error': "حدث خطأ أثناء إنشاء الفصل التكميلي. يرجى المحاولة مرة أخرى لاحقًا.",
        'back_to_chapters': "🔙 العودة إلى قائمة الفصول التكميلية",
        'no_supplementary_chapters': "لا توجد فصول تكميلية متاحة حاليًا. يرجى المحاولة مرة أخرى لاحقًا.",
        'supplementary_chapters_list': "📚 الفصول التكميلية المخصصة لك بناءً على نتيجة الاختبار:",
        'back_button': "🔙 رجوع",
        'review_generation_error': "حدث خطأ أثناء إنشاء مواد المراجعة. يرجى المحاولة مرة أخرى لاحقًا.",
        # الشروط والأحكام
        'terms_and_conditions_title': "📜 الشروط والأحكام",
        'terms_and_conditions_content': "\u26a0 إخلاء المسؤولية القانوني\n\n1. هذا البوت يقدم تحليلات فنية فقط وليست نصائح استثمارية\n2. أنت المسؤول الوحيد عن قراراتك الاستثمارية\n3. لا نتحمل مسؤولية أي خسائر مالية\n4. استخدام البوت يعني موافقتك على الشروط\n5. عدم إعطاء مفاتيح الوصول بدقة قد يؤدي لفقدان اشتراكك\n6. لا يوجد دعم فني - اتبع التعليمات بدقة\n7. المؤشرات الفنية لا يمكن أن تكون 100% دقيقة وتعتمد على بيانات تاريخية\n8. الأسواق المالية متقلبة تنطوي على مخاطر عالية\n9. لا نضمن أي أرباح أو عوائد مالية\n10. البوت قد يتعطل في أي وقت دون إشعار مسبق\n11. نحن بريئون تماما من أي استخدام للبوت في تداول العملات\n12. أي قرارات تداول تتخذها بناء على تحليلات البوت هي مسؤوليتك الكاملة\n13. مطور البوت غير مسؤول عن أي خسائر مالية قد تنتج من استخدام البوت\n14. استخدام البوت للتداول يتم على مسؤوليتك الشخصية الكاملة\n\n🔒 شروط الاستخدام:\n\u2022 البوت للتحليل الفني فقط\n\u2022 لا نضمن دقة التحليلات\n\u2022 لا نقدم استشارات مالية\n\u2022 الاشتراك غير قابل للاسترداد\n\u2022 استخدام البوت لأغراض غير مشروعة\n\u2022 يحظر مشاركة حساب الاشتراك مع الآخرين\n\u2022 نحتفظ بحق إيقاف أي حساب يخالف الشروط\n\u2022 قد نقوم بتحديث الشروط في أي وقت دون إشعار\n\u2022 البيانات المقدمة قد تكون متأخرة عن السوق الفعلي\n\u2022 لا نتحمل مسؤولية أي أعطال فنية أو انقطاع في الخدمة\n\n\u26a0 تحذيرات إضافية:\n\u2022 تداول العملات الرقمية ينطوي على مخاطر عالية\n\u2022 لا تستثمر أكثر مما يمكنك تحمل خسارته\n\u2022 قم بإجراء بحثك الخاص قبل اتخاذ أي قرار\n\u2022 كن حذرا من عمليات الاحتيال والمشاريع الوهمية\n\u2022 تأكد من فهم آلية عمل المؤشرات الفنية قبل استخدامها",
        'terms_agree_button': "✅ أوافق على الشروط والأحكام",
        'terms_decline_button': "❌ لا أوافق",
        'terms_agree_success': "شكراً لموافقتك على الشروط والأحكام",
        'terms_decline_message': "للأسف، يجب الموافقة على الشروط والأحكام لاستخدام البوت",
        'terms_required_message': "للأسف، يجب الموافقة على الشروط والأحكام لاستخدام البوت. يمكنك إعادة تشغيل البوت في أي وقت للموافقة على الشروط. 🔄",
        'language_selected': "تم اختيار اللغة بنجاح",
        'error_saving_settings': "حدث خطأ أثناء حفظ الإعدادات. الرجاء المحاولة مرة أخرى",

        # AI Learning Content Generation Prompts - Arabic
        'chapter_generation_intro': "أنت خبير في تعليم التداول بالعملات الرقمية. اشرح الموضوع التالي للمبتدئين بلغة {lang}:",
        'chapter_generation_topic': "**الموضوع:** {topic}",
        'chapter_generation_requirements': "**المطلوب:**",
        'chapter_generation_req1': "1. **شرح مفصل وواضح:** استخدم لغة بسيطة ومباشرة.",
        'chapter_generation_req2': "2. **أمثلة عملية:** قدم أمثلة لتوضيح المفاهيم.",
        'chapter_generation_req3': "3. **تنسيق Markdown:** استخدم تنسيق Markdown لتنظيم النص (عناوين، قوائم نقطية، خط عريض، إلخ).",
        'chapter_generation_req4': "4. **استخدام الإيموجيات بكثرة:** أضف إيموجيات مناسبة للمحتوى في كل فقرة وعنوان لجعله أكثر جاذبية وسهولة في القراءة. استخدم مجموعة متنوعة من الإيموجيات المناسبة للتداول مثل (📈, 📉, 📊, 💡, 💰, 🤔, 📱, 💹, 📋, 🔍, 🎯, ⚠️, ✅, ❌, 💼, 🔄, 📆, 🔔, 📢, 💪, 🧠, 🔑, 🛡️, 🚀, 💎, 🔮). ضع إيموجي مناسب قبل كل عنوان وفي بداية كل فقرة مهمة.",
        'chapter_generation_req5': "5. **التركيز على المبتدئين:** تجنب المصطلحات المعقدة قدر الإمكان أو اشرحها ببساطة.",
        'chapter_generation_req6': "6. **الرد باللغة المطلوبة فقط:** ({lang}).",
        'chapter_generation_important': "**هام:** تأكد من أن الرد بأكمله بتنسيق Markdown صحيح وجاهز للعرض مباشرة. استخدم الإيموجيات بكثرة لجعل المحتوى أكثر جاذبية وتفاعلية.",

        'quiz_generation_intro': "أنت مدرس خبير في التداول ومصمم اختبارات محترف. مهمتك هي إنشاء {num_questions} أسئلة اختبار من نوع الاختيار من متعدد باللغة {lang} لتقييم فهم المتعلم للمفاهيم التالية:",
        'quiz_generation_requirements': "**متطلبات الأسئلة:**",
        'quiz_generation_req1': "1. يجب أن تكون الأسئلة متنوعة وتغطي مفاهيم مختلفة من الفصول المذكورة أعلاه.",
        'quiz_generation_req2': "2. كل سؤال يجب أن يكون له 4 خيارات بالضبط، مع إجابة صحيحة واحدة فقط.",
        'quiz_generation_req3': "3. يجب أن تكون الأسئلة واضحة ومباشرة ومناسبة لمستوى المبتدئين.",
        'quiz_generation_req4': "4. يجب أن تكون الخيارات واقعية ومعقولة (لا تضع خيارات سخيفة أو غير منطقية).",
        'quiz_generation_format': "**تنسيق الإخراج المطلوب:**",
        'quiz_generation_format_note': "ملاحظة: تأكد من أن correct_option_id هو رقم صحيح يمثل فهرس الخيار الصحيح في مصفوفة options (0 للخيار الأول، 1 للخيار الثاني، إلخ).",
        'quiz_generation_json_only': "أنتج JSON صالح فقط بدون أي نص إضافي أو شرح.",

        'review_material_intro': "أنت مدرس خبير في التداول. المستخدم يحتاج إلى مراجعة المفاهيم التالية التي واجه صعوبة فيها خلال الاختبار:",
        'review_material_instruction': "قم بإنشاء ملخص مراجعة موجز وواضح باللغة {lang} يغطي هذه المفاهيم. استخدم تنسيق نصي بسيط (بدون Markdown معقد) واستخدم الإيموجي المناسبة لجعل المحتوى أكثر جاذبية.",
        'review_material_should_include': "يجب أن يتضمن الملخص:",
        'review_material_point1': "1. شرح مبسط للمفاهيم الأساسية",
        'review_material_point2': "2. نقاط رئيسية يجب تذكرها",
        'review_material_point3': "3. أمثلة توضيحية قصيرة",
        'review_material_point4': "4. نصائح عملية للتطبيق",
        'review_material_concise': "اجعل المحتوى موجزًا ومركزًا ومفيدًا للمراجعة السريعة.",
        'review_material_formatting_notes': "ملاحظات مهمة للتنسيق:",
        'review_material_format1': "- تجنب استخدام علامات Markdown المعقدة",
        'review_material_format2': "- استخدم النجمة (*) للنقاط فقط",
        'review_material_format3': "- تجنب استخدام الروابط",
        'review_material_format4': "- تجنب استخدام الجداول",
        'review_material_format5': "- تجنب استخدام الرموز الخاصة مثل _ و ~ و ` و | و > بكثرة",
        'review_material_format6': "- تجنب استخدام علامات النجمة المزدوجة (**) للتنسيق",
        'review_material_format7': "- استخدم الإيموجي بشكل معتدل",

        'supplementary_chapter_intro': "أنت مدرس خبير في التداول ومؤلف محتوى تعليمي. مهمتك هي إنشاء فصل تكميلي مخصص للمتعلم باللغة {lang}.",
        'supplementary_chapter_info': "**معلومات الفصل:**",
        'supplementary_chapter_title': "- العنوان: {title}",
        'supplementary_chapter_description': "- الوصف: {description}",
        'supplementary_chapter_level': "- المستوى: {level}",
        'supplementary_chapter_topic': "- الموضوع: {topic}",
        'supplementary_chapter_content_instructions': "**تعليمات المحتوى:**",
        'supplementary_chapter_inst1': "1. قم بإنشاء محتوى تعليمي موجز وفعال حول الموضوع المحدد.",
        'supplementary_chapter_inst2': "2. استخدم لغة واضحة ومباشرة تناسب مستوى المتعلم ({level}).",
        'supplementary_chapter_inst3': "3. قسّم المحتوى إلى أقسام منطقية مع عناوين فرعية.",
        'supplementary_chapter_inst4': "4. أضف أمثلة عملية موجزة لتوضيح المفاهيم.",
        'supplementary_chapter_inst5': "5. استخدم الإيموجي المناسبة لجعل المحتوى أكثر جاذبية.",
        'supplementary_chapter_inst6': "6. اختم بملخص قصير للنقاط الرئيسية.",
        'supplementary_chapter_format': "**تنسيق المحتوى:**",
        'supplementary_chapter_format1': "- استخدم تنسيق نصي بسيط (تجنب Markdown المعقد).",
        'supplementary_chapter_format2': "- استخدم الإيموجي بشكل مناسب في بداية كل قسم.",
        'supplementary_chapter_format3': "- تجنب استخدام الرموز الخاصة مثل _ و ~ و ` و | و > بكثرة.",
        'supplementary_chapter_format4': "- تجنب استخدام الروابط والجداول.",
        'supplementary_chapter_format5': "- تجنب استخدام علامات النجمة المزدوجة (**) للتنسيق.",
        'supplementary_chapter_format6': "- يجب أن يكون المحتوى موجزًا ولا يتجاوز 3000 حرف.",
        'supplementary_chapter_conclusion': "أنشئ محتوى تعليميًا موجزًا وفعالًا يساعد المتعلم على فهم الموضوع بشكل أفضل.",
        # Add other keys from trading_education.py as needed
    },
    'en': {
        'welcome_trading_ai': "Welcome to the AI Trading Learning Course! 📈",
        'add_gemini_key_prompt': "To get started, please add your Gemini API key. This will allow me to generate personalized educational content for you.",
        'add_gemini_key_button': "🔑 Add Gemini API Key",
        'gemini_access_error': "Sorry, I can't access Gemini right now. Please ensure you have added a valid API key.",
        'gemini_tutor_error': "An error occurred while trying to answer your question. Please try again later.",
        'lesson_generation_error': "Sorry, I can't access Gemini right now to generate the lesson. Please ensure you have added a valid API key.",
        'chapter_button': "Chapter {number}",
        'next_chapter_button': "Next Chapter ❯",
        'take_quiz_button': "📝 Take the Quiz",
        'ask_tutor_prompt': "You can also ask any trading-related question, and I'll do my best to answer it using Gemini.",
        'ask_tutor_button': '❓ Ask AI Tutor',
        'ask_tutor_instruction': "Please type your trading question.",
        'quiz_already_started': "📝 It looks like you've already started the quiz. Please complete answering the questions.",
        'course_completed': "You have completed the basic course and the quiz! 🎉",
        'error_starting_course': "Something went wrong. Please try starting the course again using /learn_trading_ai",
        'tutor_prompt_header': "Answer the following trading question as an expert tutor in {lang}:\n\nQuestion: {question}\n\nAnswer:",

        # AI Learning Content Generation Prompts
        'chapter_generation_intro': "You are an expert in cryptocurrency trading education. Explain the following topic for beginners in {lang}:",
        'chapter_generation_topic': "**Topic:** {topic}",
        'chapter_generation_requirements': "**Requirements:**",
        'chapter_generation_req1': "1. **Detailed and clear explanation:** Use simple and direct language.",
        'chapter_generation_req2': "2. **Practical examples:** Provide examples to illustrate concepts.",
        'chapter_generation_req3': "3. **Markdown formatting:** Use Markdown formatting to organize text (headings, bullet points, bold text, etc.).",
        'chapter_generation_req4': "4. **Use emojis extensively:** Add appropriate emojis to content in each paragraph and heading to make it more attractive and easy to read. Use a variety of trading-appropriate emojis such as (📈, 📉, 📊, 💡, 💰, 🤔, 📱, 💹, 📋, 🔍, 🎯, ⚠️, ✅, ❌, 💼, 🔄, 📆, 🔔, 📢, 💪, 🧠, 🔑, 🛡️, 🚀, 💎, 🔮). Place an appropriate emoji before each heading and at the beginning of each important paragraph.",
        'chapter_generation_req5': "5. **Focus on beginners:** Avoid complex terminology as much as possible or explain it simply.",
        'chapter_generation_req6': "6. **Respond in the requested language only:** ({lang}).",
        'chapter_generation_important': "**Important:** Make sure the entire response is in correct Markdown format and ready for direct display. Use emojis extensively to make the content more attractive and interactive.",

        'quiz_generation_intro': "You are an expert trading teacher and professional quiz designer. Your task is to create {num_questions} multiple-choice quiz questions in {lang} to assess the learner's understanding of the following concepts:",
        'quiz_generation_requirements': "**Question Requirements:**",
        'quiz_generation_req1': "1. Questions should be diverse and cover different concepts from the chapters mentioned above.",
        'quiz_generation_req2': "2. Each question must have exactly 4 options, with only one correct answer.",
        'quiz_generation_req3': "3. Questions should be clear, direct, and appropriate for beginner level.",
        'quiz_generation_req4': "4. Options should be realistic and reasonable (don't include silly or illogical options).",
        'quiz_generation_format': "**Required Output Format:**",
        'quiz_generation_format_note': "Note: Make sure correct_option_id is an integer representing the index of the correct option in the options array (0 for first option, 1 for second option, etc).",
        'quiz_generation_json_only': "Produce valid JSON only without any additional text or explanation.",

        'review_material_intro': "You are an expert trading teacher. The user needs to review the following concepts that they had difficulty with during the quiz:",
        'review_material_instruction': "Create a brief and clear review summary in {lang} covering these concepts. Use simple text formatting (without complex Markdown) and use appropriate emojis to make the content more attractive.",
        'review_material_should_include': "The summary should include:",
        'review_material_point1': "1. Simplified explanation of basic concepts",
        'review_material_point2': "2. Key points to remember",
        'review_material_point3': "3. Short illustrative examples",
        'review_material_point4': "4. Practical tips for application",
        'review_material_concise': "Make the content concise, focused, and useful for quick review.",
        'review_material_formatting_notes': "Important formatting notes:",
        'review_material_format1': "- Avoid using complex Markdown tags",
        'review_material_format2': "- Use asterisk (*) for bullet points only",
        'review_material_format3': "- Avoid using links",
        'review_material_format4': "- Avoid using tables",
        'review_material_format5': "- Avoid using special characters like _ and ~ and ` and | and > extensively",
        'review_material_format6': "- Avoid using double asterisks (**) for formatting",
        'review_material_format7': "- Use emojis moderately",

        'supplementary_chapter_intro': "You are an expert trading teacher and educational content author. Your task is to create a customized supplementary chapter for the learner in {lang}.",
        'supplementary_chapter_info': "**Chapter Information:**",
        'supplementary_chapter_title': "- Title: {title}",
        'supplementary_chapter_description': "- Description: {description}",
        'supplementary_chapter_level': "- Level: {level}",
        'supplementary_chapter_topic': "- Topic: {topic}",
        'supplementary_chapter_content_instructions': "**Content Instructions:**",
        'supplementary_chapter_inst1': "1. Create concise and effective educational content on the specified topic.",
        'supplementary_chapter_inst2': "2. Use clear and direct language appropriate for the learner's level ({level}).",
        'supplementary_chapter_inst3': "3. Divide content into logical sections with subheadings.",
        'supplementary_chapter_inst4': "4. Add brief practical examples to illustrate concepts.",
        'supplementary_chapter_inst5': "5. Use appropriate emojis to make content more attractive.",
        'supplementary_chapter_inst6': "6. End with a brief summary of key points.",
        'supplementary_chapter_format': "**Content Format:**",
        'supplementary_chapter_format1': "- Use simple text formatting (avoid complex Markdown).",
        'supplementary_chapter_format2': "- Use emojis appropriately at the beginning of each section.",
        'supplementary_chapter_format3': "- Avoid using special characters like _ and ~ and ` and | and > extensively.",
        'supplementary_chapter_format4': "- Avoid using links and tables.",
        'supplementary_chapter_format5': "- Avoid using double asterisks (**) for formatting.",
        'supplementary_chapter_format6': "- Content should be concise and not exceed 3000 characters.",
        'supplementary_chapter_conclusion': "Create concise and effective educational content that helps the learner understand the topic better.",
        'chapter_gen_prompt_header': "You are an expert trading tutor and educational content creator in {lang}. Your task is to create educational content for chapter {chapter_number} of a 10-chapter beginner trading course.",
        'guide_add_gemini_key': "Please use the /add_gemini_key command to add your API key.",
        'complete_chapters_first': "Please complete all chapters before taking the quiz.",
        'quiz_starting': "Starting the quiz...",
        'quiz_results': "Quiz finished! Score: {score}/{total}",
        'ask_again_prompt': "You can ask another question or proceed to the next chapter.",
        'operation_in_progress': "⏳ Generating chapter... Please wait.",
        'quiz_generation_in_progress': "⏳ Generating quiz... Please wait.",
        'continue_or_ask': "You can now continue with the chapter or ask another question.",
        'review_material_title': "Review Materials",
        'supplementary_chapters': "📚 Customized Supplementary Chapters",
        'back_to_main': "🏠 Back to Main Menu",
        'next_steps': "What would you like to do now?",
        'generating_chapter': "⏳ Generating supplementary chapter...",
        'chapter_not_found': "The requested chapter was not found.",
        'chapter_generation_error': "An error occurred while generating the supplementary chapter. Please try again later.",
        'back_to_chapters': "🔙 Back to Chapters List",
        'no_supplementary_chapters': "No supplementary chapters are available at this time. Please try again later.",
        'supplementary_chapters_list': "📚 Customized supplementary chapters based on your quiz results:",
        'back_button': "🔙 Back",
        'review_generation_error': "An error occurred while generating review materials. Please try again later.",
        # Terms and Conditions
        'terms_and_conditions_title': "📜 Terms and Conditions",
        'terms_and_conditions_content': "**Terms and Conditions for Using the Trading Analysis Bot**\n\n1. **General Use**: This bot is designed to provide cryptocurrency trading analysis and recommendations for informational purposes only.\n\n2. **Disclaimer**: The information provided is not financial advice, and we are not responsible for any losses that may result from using this information.\n\n3. **Subscription**: Some features are only available to subscribed users. Subscriptions are automatically renewed unless canceled.\n\n4. **Privacy**: We collect limited data to improve your experience. We will not share your personal information with third parties without your consent.\n\n5. **API**: When adding your API keys, you agree that we use them only to access your account data to provide the requested services.\n\n6. **Changes**: We reserve the right to modify these terms at any time. Users will be notified of significant changes.",
        'agree_button': "✅ I Agree to Terms",
        'disagree_button': "❌ I Disagree",
        'terms_accepted': "Thank you for accepting the terms and conditions",
        'terms_declined': "Unfortunately, you must agree to the terms to use the bot",
        'terms_required': "Unfortunately, you must agree to the terms to use the bot. You can restart the bot at any time to agree to the terms. 🔄",
        'language_selected': "Language selected successfully",
        'error_saving_settings': "An error occurred while saving settings. Please try again",
        # Add other keys from trading_education.py as needed
    }
}

def get_text(key: str, lang: str = 'ar', default: str = None, **kwargs) -> str:
    """Gets translated text, falling back to Arabic, then default, then the key itself."""
    try:
        # Try the requested language
        text = translations.get(lang, {}).get(key)
        # If not found, try Arabic
        if text is None:
            text = translations.get('ar', {}).get(key)
        # If still not found, use the provided default or the key itself
        if text is None:
            text = default if default is not None else f"[{key}]"
            logger.warning(f"Translation key '{key}' not found for lang '{lang}' or 'ar'. Used default/key.")

        return text.format(**kwargs)
    except KeyError as e:
        logger.error(f"Missing format key {e} for translation key '{key}' in lang '{lang}'")
        return default if default is not None else f"[{key} - FORMAT ERROR]"
    except Exception as e:
        logger.error(f"Error in get_text for key '{key}', lang '{lang}': {e}")
        return default if default is not None else f"[{key} - ERROR]"

def fix_bold_formatting(text: str) -> str:
    """
    إصلاح تنسيق النص العريض في تلغرام

    يقوم بإزالة النقطتين من داخل النص العريض ووضعها خارجه
    مثال: **عنوان:** -> **عنوان**:

    Args:
        text: النص المراد إصلاحه

    Returns:
        النص بعد إصلاح التنسيق
    """
    if not text:
        return text

    # إزالة النقطتين من داخل النص العريض
    text = re.sub(r'\*\*(.*?):\*\*', r'**\1**:', text)

    # إزالة النقطتين المكررة في حالة وجودها
    text = text.replace('**:', '**:')

    # استبدال أي عنوان يحتوي على نقطتين داخل النص العريض
    text = re.sub(r'\*\*(.*?):\*\*', r'**\1**:', text)

    return text